# Tool Suite - 工具集合网站

一个基于 Vue3 + Element Plus 的工具集合网站，提供多种实用的文本处理和转换功能。

## 功能特性

### 🔄 文本转换工具
- **简繁体转换**: 支持简体中文与繁体中文之间的相互转换
- **中英文翻译**: 提供中文与英文之间的双向翻译服务

### 🔐 加密解密工具
- **DES 加解密**: 支持 DES 算法的加密和解密操作，可自定义密钥

### 📝 格式化工具
- **Markdown 转 HTML**: 将 Markdown 文本转换为 HTML 格式，支持实时预览
- **JSON 格式化**: 提供 JSON 美化、压缩、转义和反转义功能
- **SQL 压缩**: 将多行 SQL 语句压缩为单行，便于复制和使用

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **UI 组件库**: Element Plus
- **构建工具**: Vite
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **HTTP 客户端**: Axios
- **容器化**: Docker + Nginx

## 快速开始

### 本地开发

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

3. **访问应用**
   打开浏览器访问 `http://localhost:5173`

### 构建生产版本

```bash
npm run build
```

### Docker 部署

1. **构建镜像**
   ```bash
   docker build -t tool-suite .
   ```

2. **运行容器**
   ```bash
   docker run -p 3000:80 tool-suite
   ```

3. **使用 Docker Compose**
   ```bash
   docker-compose up -d
   ```

## API 接口说明

本项目前端调用以下后端API接口，请确保后端服务正常运行：

### 简繁体转换
- **简体转繁体**: `POST /api/v1/text/to-traditional`
- **繁体转简体**: `POST /api/v1/text/to-simplified`
```json
{
  "content": "要转换的文本"
}
```

### 中英文翻译
- **翻译**: `POST /api/v1/text/translate`
```json
{
  "content": "要翻译的文本",
  "target_lang": "en" // 或 "zh"
}
```

### DES 加解密
- **加密**: `POST /api/v1/crypto/des/encrypt`
- **解密**: `POST /api/v1/crypto/des/decrypt`
```json
{
  "text": "要处理的文本",
  "key": "8位密钥"
}
```

### HTML 转 Markdown
- **转换**: `POST /api/v1/text/html-to-markdown`
```json
{
  "content": "HTML内容"
}
```

### SQL 压缩
- **压缩**: `POST /api/v1/text/sql-compress`
```json
{
  "content": "要压缩的SQL语句"
}
```

所有接口返回格式：
```json
{
  "result": "处理结果"
}
```
