<template>
  <div class="markdown-container">
    <el-card class="markdown-card">
      <template #header>
        <div class="card-header">
          <el-icon class="card-icon"><Document /></el-icon>
          <span>Markdown 转 HTML</span>
        </div>
      </template>
      
      <div class="markdown-content">
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="input-section">
              <div class="section-header">
                <h3>Markdown 输入</h3>
                <div class="header-buttons">
                  <el-button size="small" @click="insertTemplate">
                    <el-icon><DocumentAdd /></el-icon>
                    插入模板
                  </el-button>
                </div>
              </div>
              <el-input
                v-model="markdownText"
                type="textarea"
                :rows="15"
                placeholder="请输入 Markdown 文本..."
                class="input-textarea"
              />
              <div class="button-group">
                <el-button type="primary" @click="convertMarkdown" :loading="loading">
                  <el-icon><Promotion /></el-icon>
                  转换为 HTML
                </el-button>
                <el-button @click="clearText">
                  <el-icon><Delete /></el-icon>
                  清空
                </el-button>
                <el-button @click="previewMode = !previewMode">
                  <el-icon><View /></el-icon>
                  {{ previewMode ? '查看代码' : '预览效果' }}
                </el-button>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="output-section">
              <div class="section-header">
                <h3>HTML 输出</h3>
                <el-button size="small" @click="copyResult" :disabled="!htmlOutput">
                  <el-icon><DocumentCopy /></el-icon>
                  复制 HTML
                </el-button>
              </div>
              
              <div v-if="!previewMode" class="html-code">
                <el-input
                  v-model="htmlOutput"
                  type="textarea"
                  :rows="15"
                  readonly
                  placeholder="HTML 代码将显示在这里..."
                  class="output-textarea"
                />
              </div>
              
              <div v-else class="html-preview">
                <div class="preview-container" v-html="htmlOutput"></div>
              </div>
              
              <div class="result-info">
                <span v-if="htmlOutput">
                  HTML 长度: {{ htmlOutput.length }} 字符
                </span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const markdownText = ref('')
const htmlOutput = ref('')
const loading = ref(false)
const previewMode = ref(false)

const markdownTemplate = `# 标题 1
## 标题 2
### 标题 3

这是一个段落，包含 **粗体文本** 和 *斜体文本*。

- 列表项 1
- 列表项 2
- 列表项 3

1. 有序列表项 1
2. 有序列表项 2
3. 有序列表项 3

\`\`\`javascript
// 代码块示例
function hello() {
    console.log("Hello, World!");
}
\`\`\`

> 这是一个引用块

[链接文本](https://example.com)

| 表格标题1 | 表格标题2 |
|----------|----------|
| 单元格1   | 单元格2   |
| 单元格3   | 单元格4   |
`

const convertMarkdown = async () => {
  if (!markdownText.value.trim()) {
    ElMessage.warning('请输入 Markdown 文本')
    return
  }
  
  loading.value = true
  try {
    const response = await axios.post('/api/markdown-to-html', {
      markdown: markdownText.value
    })
    
    htmlOutput.value = response.data.html
    ElMessage.success('转换成功')
  } catch (error) {
    console.error('转换失败:', error)
    ElMessage.error('转换失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const insertTemplate = () => {
  markdownText.value = markdownTemplate
  ElMessage.success('模板已插入')
}

const clearText = () => {
  markdownText.value = ''
  htmlOutput.value = ''
  previewMode.value = false
}

const copyResult = async () => {
  try {
    await navigator.clipboard.writeText(htmlOutput.value)
    ElMessage.success('HTML 代码复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped>
.markdown-container {
  max-width: 1200px;
  margin: 0 auto;
}

.markdown-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 18px;
}

.card-icon {
  font-size: 20px;
  color: #409eff;
}

.markdown-content {
  padding: 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.input-textarea,
.output-textarea {
  margin-bottom: 16px;
  font-family: 'Courier New', monospace;
}

.html-preview {
  height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.preview-container {
  padding: 16px;
  background-color: white;
  min-height: 100%;
}

.preview-container :deep(h1),
.preview-container :deep(h2),
.preview-container :deep(h3) {
  color: #303133;
  margin-top: 0;
}

.preview-container :deep(code) {
  background-color: #f5f7fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.preview-container :deep(pre) {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
}

.preview-container :deep(blockquote) {
  border-left: 4px solid #409eff;
  padding-left: 16px;
  margin: 16px 0;
  color: #606266;
}

.preview-container :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.preview-container :deep(th),
.preview-container :deep(td) {
  border: 1px solid #dcdfe6;
  padding: 8px 12px;
  text-align: left;
}

.preview-container :deep(th) {
  background-color: #f5f7fa;
  font-weight: 600;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.result-info {
  text-align: right;
  color: #909399;
  font-size: 12px;
}

.output-section {
  border-left: 1px solid #e4e7ed;
  padding-left: 24px;
}
</style>
