version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - tool-suite-network
    restart: unless-stopped

  backend:
    # 这里需要替换为你的后端服务镜像
    # image: your-backend-image:latest
    # 或者如果后端代码在同一个仓库中
    # build: ./backend
    image: placeholder-backend:latest
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
      # 添加其他环境变量
    networks:
      - tool-suite-network
    restart: unless-stopped

networks:
  tool-suite-network:
    driver: bridge

# 如果需要持久化数据，可以添加 volumes
# volumes:
#   backend-data:
